0|whaconnect-backend  | erro ao enviar mensagem na company 1 -  *<PERSON>.:*
0|whaconnect-backend  | mensaje nuevo Ticket {
0|whaconnect-backend  |   dataValues: {
0|whaconnect-backend  |     id: 1,
0|whaconnect-backend  |     uuid: 'b0649407-6818-46eb-ab9a-6eba8cd532ea',
0|whaconnect-backend  |     queueId: null,
0|whaconnect-backend  |     lastFlowId: null,
0|whaconnect-backend  |     flowStopped: null,
0|whaconnect-backend  |     dataWebhook: null,
0|whaconnect-backend  |     flowWebhook: false,
0|whaconnect-backend  |     isGroup: true,
0|whaconnect-backend  |     channel: 'whatsapp',
0|whaconnect-backend  |     status: 'group',
0|whaconnect-backend  |     contactId: 1,
0|whaconnect-backend  |     useIntegration: false,
0|whaconnect-backend  |     lastMessage: 'Mensaje desde el celular',
0|whaconnect-backend  |     updatedAt: 2025-08-27T18:14:43.978Z,
0|whaconnect-backend  |     unreadMessages: 0,
0|whaconnect-backend  |     companyId: 1,
0|whaconnect-backend  |     whatsappId: 1,
0|whaconnect-backend  |     imported: null,
0|whaconnect-backend  |     lgpdAcceptedAt: null,
0|whaconnect-backend  |     amountUsedBotQueues: 0,
0|whaconnect-backend  |     integrationId: null,
0|whaconnect-backend  |     userId: null,
0|whaconnect-backend  |     amountUsedBotQueuesNPS: 0,
0|whaconnect-backend  |     lgpdSendMessageAt: null,
0|whaconnect-backend  |     isBot: false,
0|whaconnect-backend  |     typebotSessionId: null,
0|whaconnect-backend  |     typebotStatus: false,
0|whaconnect-backend  |     sendInactiveMessage: false,
0|whaconnect-backend  |     fromMe: false,
0|whaconnect-backend  |     isOutOfHour: false,
0|whaconnect-backend  |     isActiveDemand: false,
0|whaconnect-backend  |     typebotSessionTime: null,
0|whaconnect-backend  |     contact: Contact {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       extraInfo: [],
0|whaconnect-backend  |       tags: [],
0|whaconnect-backend  |       wallets: []
0|whaconnect-backend  |     },
0|whaconnect-backend  |     queue: null,
0|whaconnect-backend  |     user: null,
0|whaconnect-backend  |     tags: [],
0|whaconnect-backend  |     whatsapp: Whatsapp {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false
0|whaconnect-backend  |     },
0|whaconnect-backend  |     company: Company {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       plan: [Plan]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     queueIntegration: null,
0|whaconnect-backend  |     ticketTags: []
0|whaconnect-backend  |   },
0|whaconnect-backend  |   _previousDataValues: {
0|whaconnect-backend  |     id: 1,
0|whaconnect-backend  |     uuid: 'b0649407-6818-46eb-ab9a-6eba8cd532ea',
0|whaconnect-backend  |     queueId: null,
0|whaconnect-backend  |     lastFlowId: null,
0|whaconnect-backend  |     flowStopped: null,
0|whaconnect-backend  |     dataWebhook: null,
0|whaconnect-backend  |     flowWebhook: false,
0|whaconnect-backend  |     isGroup: true,
0|whaconnect-backend  |     channel: 'whatsapp',
0|whaconnect-backend  |     status: 'group',
0|whaconnect-backend  |     contactId: 1,
0|whaconnect-backend  |     useIntegration: false,
0|whaconnect-backend  |     lastMessage: 'Mensaje desde el celular',
0|whaconnect-backend  |     updatedAt: 2025-08-27T18:14:43.978Z,
0|whaconnect-backend  |     unreadMessages: 0,
0|whaconnect-backend  |     companyId: 1,
0|whaconnect-backend  |     whatsappId: 1,
0|whaconnect-backend  |     imported: null,
0|whaconnect-backend  |     lgpdAcceptedAt: null,
0|whaconnect-backend  |     amountUsedBotQueues: 0,
0|whaconnect-backend  |     integrationId: null,
0|whaconnect-backend  |     userId: null,
0|whaconnect-backend  |     amountUsedBotQueuesNPS: 0,
0|whaconnect-backend  |     lgpdSendMessageAt: null,
0|whaconnect-backend  |     isBot: false,
0|whaconnect-backend  |     typebotSessionId: null,
0|whaconnect-backend  |     typebotStatus: false,
0|whaconnect-backend  |     sendInactiveMessage: false,
0|whaconnect-backend  |     fromMe: false,
0|whaconnect-backend  |     isOutOfHour: false,
0|whaconnect-backend  |     isActiveDemand: false,
0|whaconnect-backend  |     typebotSessionTime: null,
0|whaconnect-backend  |     contact: Contact {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       extraInfo: [],
0|whaconnect-backend  |       tags: [],
0|whaconnect-backend  |       wallets: []
0|whaconnect-backend  |     },
0|whaconnect-backend  |     queue: null,
0|whaconnect-backend  |     user: null,
0|whaconnect-backend  |     tags: [],
0|whaconnect-backend  |     whatsapp: Whatsapp {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false
0|whaconnect-backend  |     },
0|whaconnect-backend  |     company: Company {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       plan: [Plan]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     queueIntegration: null,
0|whaconnect-backend  |     ticketTags: []
0|whaconnect-backend  |   },
0|whaconnect-backend  |   _changed: {},
0|whaconnect-backend  |   _modelOptions: {
0|whaconnect-backend  |     timestamps: true,
0|whaconnect-backend  |     validate: {},
0|whaconnect-backend  |     freezeTableName: false,
0|whaconnect-backend  |     underscored: false,
0|whaconnect-backend  |     paranoid: false,
0|whaconnect-backend  |     rejectOnEmpty: false,
0|whaconnect-backend  |     whereCollection: { id: '1', companyId: 1 },
0|whaconnect-backend  |     schema: null,
0|whaconnect-backend  |     schemaDelimiter: '',
0|whaconnect-backend  |     defaultScope: {},
0|whaconnect-backend  |     scopes: {},
0|whaconnect-backend  |     indexes: [],
0|whaconnect-backend  |     name: { plural: 'Tickets', singular: 'Ticket' },
0|whaconnect-backend  |     omitNull: false,
0|whaconnect-backend  |     charset: 'utf8mb4',
0|whaconnect-backend  |     collate: 'utf8mb4_bin',
0|whaconnect-backend  |     updatedAt: 'updatedAt',
0|whaconnect-backend  |     sequelize: Sequelize {
0|whaconnect-backend  |       options: [Object],
0|whaconnect-backend  |       config: [Object],
0|whaconnect-backend  |       dialect: [PostgresDialect],
0|whaconnect-backend  |       queryInterface: [QueryInterface],
0|whaconnect-backend  |       models: [Object],
0|whaconnect-backend  |       modelManager: [ModelManager],
0|whaconnect-backend  |       connectionManager: [ConnectionManager],
0|whaconnect-backend  |       importCache: {},
0|whaconnect-backend  |       repositoryMode: false
0|whaconnect-backend  |     },
0|whaconnect-backend  |     hooks: { beforeCreate: [Array] }
0|whaconnect-backend  |   },
0|whaconnect-backend  |   _options: {
0|whaconnect-backend  |     isNewRecord: false,
0|whaconnect-backend  |     _schema: null,
0|whaconnect-backend  |     _schemaDelimiter: '',
0|whaconnect-backend  |     include: [
0|whaconnect-backend  |       [Object], [Object],
0|whaconnect-backend  |       [Object], [Object],
0|whaconnect-backend  |       [Object], [Object],
0|whaconnect-backend  |       [Object], [Object]
0|whaconnect-backend  |     ],
0|whaconnect-backend  |     includeNames: [
0|whaconnect-backend  |       'contact',
0|whaconnect-backend  |       'queue',
0|whaconnect-backend  |       'user',
0|whaconnect-backend  |       'tags',
0|whaconnect-backend  |       'whatsapp',
0|whaconnect-backend  |       'company',
0|whaconnect-backend  |       'queueIntegration',
0|whaconnect-backend  |       'ticketTags'
0|whaconnect-backend  |     ],
0|whaconnect-backend  |     includeMap: {
0|whaconnect-backend  |       contact: [Object],
0|whaconnect-backend  |       queue: [Object],
0|whaconnect-backend  |       user: [Object],
0|whaconnect-backend  |       tags: [Object],
0|whaconnect-backend  |       whatsapp: [Object],
0|whaconnect-backend  |       company: [Object],
0|whaconnect-backend  |       queueIntegration: [Object],
0|whaconnect-backend  |       ticketTags: [Object]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     includeValidated: true,
0|whaconnect-backend  |     attributes: [
0|whaconnect-backend  |       'id',
0|whaconnect-backend  |       'uuid',
0|whaconnect-backend  |       'queueId',
0|whaconnect-backend  |       'lastFlowId',
0|whaconnect-backend  |       'flowStopped',
0|whaconnect-backend  |       'dataWebhook',
0|whaconnect-backend  |       'flowWebhook',
0|whaconnect-backend  |       'isGroup',
0|whaconnect-backend  |       'channel',
0|whaconnect-backend  |       'status',
0|whaconnect-backend  |       'contactId',
0|whaconnect-backend  |       'useIntegration',
0|whaconnect-backend  |       'lastMessage',
0|whaconnect-backend  |       'updatedAt',
0|whaconnect-backend  |       'unreadMessages',
0|whaconnect-backend  |       'companyId',
0|whaconnect-backend  |       'whatsappId',
0|whaconnect-backend  |       'imported',
0|whaconnect-backend  |       'lgpdAcceptedAt',
0|whaconnect-backend  |       'amountUsedBotQueues',
0|whaconnect-backend  |       'useIntegration',
0|whaconnect-backend  |       'integrationId',
0|whaconnect-backend  |       'userId',
0|whaconnect-backend  |       'amountUsedBotQueuesNPS',
0|whaconnect-backend  |       'lgpdSendMessageAt',
0|whaconnect-backend  |       'isBot',
0|whaconnect-backend  |       'typebotSessionId',
0|whaconnect-backend  |       'typebotStatus',
0|whaconnect-backend  |       'sendInactiveMessage',
0|whaconnect-backend  |       'queueId',
0|whaconnect-backend  |       'fromMe',
0|whaconnect-backend  |       'isOutOfHour',
0|whaconnect-backend  |       'isActiveDemand',
0|whaconnect-backend  |       'typebotSessionTime'
0|whaconnect-backend  |     ],
0|whaconnect-backend  |     raw: true
0|whaconnect-backend  |   },
0|whaconnect-backend  |   isNewRecord: false,
0|whaconnect-backend  |   contact: Contact {
0|whaconnect-backend  |     dataValues: {
0|whaconnect-backend  |       id: 1,
0|whaconnect-backend  |       companyId: 1,
0|whaconnect-backend  |       name: 'Test grupo WhatsApp ',
0|whaconnect-backend  |       number: '120363403313320196',
0|whaconnect-backend  |       email: '',
0|whaconnect-backend  |       profilePicUrl: 'http://localhost:3333/nopicture.png',
0|whaconnect-backend  |       acceptAudioMessage: true,
0|whaconnect-backend  |       active: true,
0|whaconnect-backend  |       disableBot: false,
0|whaconnect-backend  |       remoteJid: '<EMAIL>',
0|whaconnect-backend  |       urlPicture: '1756318482628.jpeg',
0|whaconnect-backend  |       lgpdAcceptedAt: null,
0|whaconnect-backend  |       extraInfo: [],
0|whaconnect-backend  |       tags: [],
0|whaconnect-backend  |       wallets: []
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _previousDataValues: {
0|whaconnect-backend  |       id: 1,
0|whaconnect-backend  |       companyId: 1,
0|whaconnect-backend  |       name: 'Test grupo WhatsApp ',
0|whaconnect-backend  |       number: '120363403313320196',
0|whaconnect-backend  |       email: '',
0|whaconnect-backend  |       profilePicUrl: 'http://localhost:3333/nopicture.png',
0|whaconnect-backend  |       acceptAudioMessage: true,
0|whaconnect-backend  |       active: true,
0|whaconnect-backend  |       disableBot: false,
0|whaconnect-backend  |       remoteJid: '<EMAIL>',
0|whaconnect-backend  |       urlPicture: '1756318482628.jpeg',
0|whaconnect-backend  |       lgpdAcceptedAt: null,
0|whaconnect-backend  |       extraInfo: [],
0|whaconnect-backend  |       tags: [],
0|whaconnect-backend  |       wallets: []
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _changed: {},
0|whaconnect-backend  |     _modelOptions: {
0|whaconnect-backend  |       timestamps: true,
0|whaconnect-backend  |       validate: {},
0|whaconnect-backend  |       freezeTableName: false,
0|whaconnect-backend  |       underscored: false,
0|whaconnect-backend  |       paranoid: false,
0|whaconnect-backend  |       rejectOnEmpty: false,
0|whaconnect-backend  |       whereCollection: [Object],
0|whaconnect-backend  |       schema: null,
0|whaconnect-backend  |       schemaDelimiter: '',
0|whaconnect-backend  |       defaultScope: {},
0|whaconnect-backend  |       scopes: {},
0|whaconnect-backend  |       indexes: [],
0|whaconnect-backend  |       name: [Object],
0|whaconnect-backend  |       omitNull: false,
0|whaconnect-backend  |       charset: 'utf8mb4',
0|whaconnect-backend  |       collate: 'utf8mb4_bin',
0|whaconnect-backend  |       createdAt: 'createdAt',
0|whaconnect-backend  |       updatedAt: 'updatedAt',
0|whaconnect-backend  |       sequelize: [Sequelize],
0|whaconnect-backend  |       hooks: {}
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _options: {
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       _schema: null,
0|whaconnect-backend  |       _schemaDelimiter: '',
0|whaconnect-backend  |       include: [Array],
0|whaconnect-backend  |       includeNames: [Array],
0|whaconnect-backend  |       includeMap: [Object],
0|whaconnect-backend  |       includeValidated: true,
0|whaconnect-backend  |       raw: true,
0|whaconnect-backend  |       attributes: [Array]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     isNewRecord: false,
0|whaconnect-backend  |     extraInfo: [],
0|whaconnect-backend  |     tags: [],
0|whaconnect-backend  |     wallets: []
0|whaconnect-backend  |   },
0|whaconnect-backend  |   queue: null,
0|whaconnect-backend  |   user: null,
0|whaconnect-backend  |   tags: [],
0|whaconnect-backend  |   whatsapp: Whatsapp {
0|whaconnect-backend  |     dataValues: {
0|whaconnect-backend  |       id: 1,
0|whaconnect-backend  |       name: 'Demo',
0|whaconnect-backend  |       groupAsTicket: 'disabled',
0|whaconnect-backend  |       greetingMediaAttachment: null,
0|whaconnect-backend  |       facebookUserToken: null,
0|whaconnect-backend  |       facebookUserId: null,
0|whaconnect-backend  |       status: 'CONNECTED'
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _previousDataValues: {
0|whaconnect-backend  |       id: 1,
0|whaconnect-backend  |       name: 'Demo',
0|whaconnect-backend  |       groupAsTicket: 'disabled',
0|whaconnect-backend  |       greetingMediaAttachment: null,
0|whaconnect-backend  |       facebookUserToken: null,
0|whaconnect-backend  |       facebookUserId: null,
0|whaconnect-backend  |       status: 'CONNECTED'
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _changed: {},
0|whaconnect-backend  |     _modelOptions: {
0|whaconnect-backend  |       timestamps: true,
0|whaconnect-backend  |       validate: {},
0|whaconnect-backend  |       freezeTableName: false,
0|whaconnect-backend  |       underscored: false,
0|whaconnect-backend  |       paranoid: false,
0|whaconnect-backend  |       rejectOnEmpty: false,
0|whaconnect-backend  |       whereCollection: [Object],
0|whaconnect-backend  |       schema: null,
0|whaconnect-backend  |       schemaDelimiter: '',
0|whaconnect-backend  |       defaultScope: {},
0|whaconnect-backend  |       scopes: {},
0|whaconnect-backend  |       indexes: [],
0|whaconnect-backend  |       name: [Object],
0|whaconnect-backend  |       omitNull: false,
0|whaconnect-backend  |       charset: 'utf8mb4',
0|whaconnect-backend  |       collate: 'utf8mb4_bin',
0|whaconnect-backend  |       createdAt: 'createdAt',
0|whaconnect-backend  |       updatedAt: 'updatedAt',
0|whaconnect-backend  |       sequelize: [Sequelize],
0|whaconnect-backend  |       hooks: {}
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _options: {
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       _schema: null,
0|whaconnect-backend  |       _schemaDelimiter: '',
0|whaconnect-backend  |       include: undefined,
0|whaconnect-backend  |       includeNames: undefined,
0|whaconnect-backend  |       includeMap: undefined,
0|whaconnect-backend  |       includeValidated: true,
0|whaconnect-backend  |       raw: true,
0|whaconnect-backend  |       attributes: [Array]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     isNewRecord: false
0|whaconnect-backend  |   },
0|whaconnect-backend  |   company: Company {
0|whaconnect-backend  |     dataValues: { id: 1, name: 'WhaConnect', plan: [Plan] },
0|whaconnect-backend  |     _previousDataValues: { id: 1, name: 'WhaConnect', plan: [Plan] },
0|whaconnect-backend  |     _changed: {},
0|whaconnect-backend  |     _modelOptions: {
0|whaconnect-backend  |       timestamps: true,
0|whaconnect-backend  |       validate: {},
0|whaconnect-backend  |       freezeTableName: false,
0|whaconnect-backend  |       underscored: false,
0|whaconnect-backend  |       paranoid: false,
0|whaconnect-backend  |       rejectOnEmpty: false,
0|whaconnect-backend  |       whereCollection: [Object],
0|whaconnect-backend  |       schema: null,
0|whaconnect-backend  |       schemaDelimiter: '',
0|whaconnect-backend  |       defaultScope: {},
0|whaconnect-backend  |       scopes: {},
0|whaconnect-backend  |       indexes: [],
0|whaconnect-backend  |       name: [Object],
0|whaconnect-backend  |       omitNull: false,
0|whaconnect-backend  |       charset: 'utf8mb4',
0|whaconnect-backend  |       collate: 'utf8mb4_bin',
0|whaconnect-backend  |       createdAt: 'createdAt',
0|whaconnect-backend  |       updatedAt: 'updatedAt',
0|whaconnect-backend  |       sequelize: [Sequelize],
0|whaconnect-backend  |       hooks: {}
0|whaconnect-backend  |     },
0|whaconnect-backend  |     _options: {
0|whaconnect-backend  |       isNewRecord: false,
0|whaconnect-backend  |       _schema: null,
0|whaconnect-backend  |       _schemaDelimiter: '',
0|whaconnect-backend  |       include: [Array],
0|whaconnect-backend  |       includeNames: [Array],
0|whaconnect-backend  |       includeMap: [Object],
0|whaconnect-backend  |       includeValidated: true,
0|whaconnect-backend  |       raw: true,
0|whaconnect-backend  |       attributes: [Array]
0|whaconnect-backend  |     },
0|whaconnect-backend  |     isNewRecord: false,
0|whaconnect-backend  |     plan: Plan {
0|whaconnect-backend  |       dataValues: [Object],
0|whaconnect-backend  |       _previousDataValues: [Object],
0|whaconnect-backend  |       _changed: {},
0|whaconnect-backend  |       _modelOptions: [Object],
0|whaconnect-backend  |       _options: [Object],
0|whaconnect-backend  |       isNewRecord: false
0|whaconnect-backend  |     }
0|whaconnect-backend  |   },
0|whaconnect-backend  |   queueIntegration: null,
0|whaconnect-backend  |   ticketTags: []
0|whaconnect-backend  | } null undefined undefined false
0|whaconnect-backend  | Error: not-acceptable
0|whaconnect-backend  |     at assertNodeErrorFree (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/WABinary/generic-utils.js:56:15)
0|whaconnect-backend  |     at query (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/socket.js:143:48)
0|whaconnect-backend  |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
0|whaconnect-backend  |     at async assertSessions (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:201:28)
0|whaconnect-backend  |     at async /home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:370:21
0|whaconnect-backend  |     at async Object.transaction (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Utils/auth-utils.js:135:26)
0|whaconnect-backend  |     at async relayMessage (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:306:9)
0|whaconnect-backend  |     at async Object.sendMessage (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:681:17)
0|whaconnect-backend  |     at async SendWhatsAppMessage (/home/<USER>/whaconnect/backend/dist/services/WbotServices/SendWhatsAppMessage.js:108:29)
0|whaconnect-backend  |     at async store (/home/<USER>/whaconnect/backend/dist/controllers/MessageController.js:137:17) {
0|whaconnect-backend  |   data: 406,
0|whaconnect-backend  |   isBoom: true,
0|whaconnect-backend  |   isServer: true,
0|whaconnect-backend  |   output: {
0|whaconnect-backend  |     statusCode: 500,
0|whaconnect-backend  |     payload: {
0|whaconnect-backend  |       statusCode: 500,
0|whaconnect-backend  |       error: 'Internal Server Error',
0|whaconnect-backend  |       message: 'An internal server error occurred'
0|whaconnect-backend  |     },
0|whaconnect-backend  |     headers: {}
0|whaconnect-backend  |   }
0|whaconnect-backend  | }
0|whaconnect-backend  | AppError { message: 'ERR_SENDING_WAPP_MSG', statusCode: 400 }