 Enviando mensaje <NAME_EMAIL> - Validaciones deshabilitadas temporalmente
 erro ao enviar mensagem na company 1 -  *<PERSON>.:*
 mensaje Ticket {
   dataValues: {
     id: 1,
     uuid: 'b0649407-6818-46eb-ab9a-6eba8cd532ea',
     queueId: null,
     lastFlowId: null,
     flowStopped: null,
     dataWebhook: null,
     flowWebhook: false,
     isGroup: true,
     channel: 'whatsapp',
     status: 'group',
     contactId: 1,
     useIntegration: false,
     lastMessage: 'Mensaje desde el celular',
     updatedAt: 2025-08-27T18:14:43.978Z,
     unreadMessages: 0,
     companyId: 1,
     whatsappId: 1,
     imported: null,
     lgpdAcceptedAt: null,
     amountUsedBotQueues: 0,
     integrationId: null,
     userId: null,
     amountUsedBotQueuesNPS: 0,
     lgpdSendMessageAt: null,
     isBot: false,
     typebotSessionId: null,
     typebotStatus: false,
     sendInactiveMessage: false,
     fromMe: false,
     isOutOfHour: false,
     isActiveDemand: false,
     typebotSessionTime: null,
     contact: Contact {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false,
       extraInfo: [],
       tags: [],
       wallets: []
     },
     queue: null,
     user: null,
     tags: [],
     whatsapp: Whatsapp {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false
     },
     company: Company {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false,
       plan: [Plan]
     },
     queueIntegration: null,
     ticketTags: []
   },
   _previousDataValues: {
     id: 1,
     uuid: 'b0649407-6818-46eb-ab9a-6eba8cd532ea',
     queueId: null,
     lastFlowId: null,
     flowStopped: null,
     dataWebhook: null,
     flowWebhook: false,
     isGroup: true,
     channel: 'whatsapp',
     status: 'group',
     contactId: 1,
     useIntegration: false,
     lastMessage: 'Mensaje desde el celular',
     updatedAt: 2025-08-27T18:14:43.978Z,
     unreadMessages: 0,
     companyId: 1,
     whatsappId: 1,
     imported: null,
     lgpdAcceptedAt: null,
     amountUsedBotQueues: 0,
     integrationId: null,
     userId: null,
     amountUsedBotQueuesNPS: 0,
     lgpdSendMessageAt: null,
     isBot: false,
     typebotSessionId: null,
     typebotStatus: false,
     sendInactiveMessage: false,
     fromMe: false,
     isOutOfHour: false,
     isActiveDemand: false,
     typebotSessionTime: null,
     contact: Contact {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false,
       extraInfo: [],
       tags: [],
       wallets: []
     },
     queue: null,
     user: null,
     tags: [],
     whatsapp: Whatsapp {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false
     },
     company: Company {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false,
       plan: [Plan]
     },
     queueIntegration: null,
     ticketTags: []
   },
   _changed: {},
   _modelOptions: {
     timestamps: true,
     validate: {},
     freezeTableName: false,
     underscored: false,
     paranoid: false,
     rejectOnEmpty: false,
     whereCollection: { id: '1', companyId: 1 },
     schema: null,
     schemaDelimiter: '',
     defaultScope: {},
     scopes: {},
     indexes: [],
     name: { plural: 'Tickets', singular: 'Ticket' },
     omitNull: false,
     charset: 'utf8mb4',
     collate: 'utf8mb4_bin',
     updatedAt: 'updatedAt',
     sequelize: Sequelize {
       options: [Object],
       config: [Object],
       dialect: [PostgresDialect],
       queryInterface: [QueryInterface],
       models: [Object],
       modelManager: [ModelManager],
       connectionManager: [ConnectionManager],
       importCache: {},
       repositoryMode: false
     },
     hooks: { beforeCreate: [Array] }
   },
   _options: {
     isNewRecord: false,
     _schema: null,
     _schemaDelimiter: '',
     include: [
       [Object], [Object],
       [Object], [Object],
       [Object], [Object],
       [Object], [Object]
     ],
     includeNames: [
       'contact',
       'queue',
       'user',
       'tags',
       'whatsapp',
       'company',
       'queueIntegration',
       'ticketTags'
     ],
     includeMap: {
       contact: [Object],
       queue: [Object],
       user: [Object],
       tags: [Object],
       whatsapp: [Object],
       company: [Object],
       queueIntegration: [Object],
       ticketTags: [Object]
     },
     includeValidated: true,
     attributes: [
       'id',
       'uuid',
       'queueId',
       'lastFlowId',
       'flowStopped',
       'dataWebhook',
       'flowWebhook',
       'isGroup',
       'channel',
       'status',
       'contactId',
       'useIntegration',
       'lastMessage',
       'updatedAt',
       'unreadMessages',
       'companyId',
       'whatsappId',
       'imported',
       'lgpdAcceptedAt',
       'amountUsedBotQueues',
       'useIntegration',
       'integrationId',
       'userId',
       'amountUsedBotQueuesNPS',
       'lgpdSendMessageAt',
       'isBot',
       'typebotSessionId',
       'typebotStatus',
       'sendInactiveMessage',
       'queueId',
       'fromMe',
       'isOutOfHour',
       'isActiveDemand',
       'typebotSessionTime'
     ],
     raw: true
   },
   isNewRecord: false,
   contact: Contact {
     dataValues: {
       id: 1,
       companyId: 1,
       name: 'Test grupo WhatsApp ',
       number: '120363403313320196',
       email: '',
       profilePicUrl: 'http://localhost:3333/nopicture.png',
       acceptAudioMessage: true,
       active: true,
       disableBot: false,
       remoteJid: '<EMAIL>',
       urlPicture: '1756318482628.jpeg',
       lgpdAcceptedAt: null,
       extraInfo: [],
       tags: [],
       wallets: []
     },
     _previousDataValues: {
       id: 1,
       companyId: 1,
       name: 'Test grupo WhatsApp ',
       number: '120363403313320196',
       email: '',
       profilePicUrl: 'http://localhost:3333/nopicture.png',
       acceptAudioMessage: true,
       active: true,
       disableBot: false,
       remoteJid: '<EMAIL>',
       urlPicture: '1756318482628.jpeg',
       lgpdAcceptedAt: null,
       extraInfo: [],
       tags: [],
       wallets: []
     },
     _changed: {},
     _modelOptions: {
       timestamps: true,
       validate: {},
       freezeTableName: false,
       underscored: false,
       paranoid: false,
       rejectOnEmpty: false,
       whereCollection: [Object],
       schema: null,
       schemaDelimiter: '',
       defaultScope: {},
       scopes: {},
       indexes: [],
       name: [Object],
       omitNull: false,
       charset: 'utf8mb4',
       collate: 'utf8mb4_bin',
       createdAt: 'createdAt',
       updatedAt: 'updatedAt',
       sequelize: [Sequelize],
       hooks: {}
     },
     _options: {
       isNewRecord: false,
       _schema: null,
       _schemaDelimiter: '',
       include: [Array],
       includeNames: [Array],
       includeMap: [Object],
       includeValidated: true,
       raw: true,
       attributes: [Array]
     },
     isNewRecord: false,
     extraInfo: [],
     tags: [],
     wallets: []
   },
   queue: null,
   user: null,
   tags: [],
   whatsapp: Whatsapp {
     dataValues: {
       id: 1,
       name: 'Demo',
       groupAsTicket: 'disabled',
       greetingMediaAttachment: null,
       facebookUserToken: null,
       facebookUserId: null,
       status: 'CONNECTED'
     },
     _previousDataValues: {
       id: 1,
       name: 'Demo',
       groupAsTicket: 'disabled',
       greetingMediaAttachment: null,
       facebookUserToken: null,
       facebookUserId: null,
       status: 'CONNECTED'
     },
     _changed: {},
     _modelOptions: {
       timestamps: true,
       validate: {},
       freezeTableName: false,
       underscored: false,
       paranoid: false,
       rejectOnEmpty: false,
       whereCollection: [Object],
       schema: null,
       schemaDelimiter: '',
       defaultScope: {},
       scopes: {},
       indexes: [],
       name: [Object],
       omitNull: false,
       charset: 'utf8mb4',
       collate: 'utf8mb4_bin',
       createdAt: 'createdAt',
       updatedAt: 'updatedAt',
       sequelize: [Sequelize],
       hooks: {}
     },
     _options: {
       isNewRecord: false,
       _schema: null,
       _schemaDelimiter: '',
       include: undefined,
       includeNames: undefined,
       includeMap: undefined,
       includeValidated: true,
       raw: true,
       attributes: [Array]
     },
     isNewRecord: false
   },
   company: Company {
     dataValues: { id: 1, name: 'WhaConnect', plan: [Plan] },
     _previousDataValues: { id: 1, name: 'WhaConnect', plan: [Plan] },
     _changed: {},
     _modelOptions: {
       timestamps: true,
       validate: {},
       freezeTableName: false,
       underscored: false,
       paranoid: false,
       rejectOnEmpty: false,
       whereCollection: [Object],
       schema: null,
       schemaDelimiter: '',
       defaultScope: {},
       scopes: {},
       indexes: [],
       name: [Object],
       omitNull: false,
       charset: 'utf8mb4',
       collate: 'utf8mb4_bin',
       createdAt: 'createdAt',
       updatedAt: 'updatedAt',
       sequelize: [Sequelize],
       hooks: {}
     },
     _options: {
       isNewRecord: false,
       _schema: null,
       _schemaDelimiter: '',
       include: [Array],
       includeNames: [Array],
       includeMap: [Object],
       includeValidated: true,
       raw: true,
       attributes: [Array]
     },
     isNewRecord: false,
     plan: Plan {
       dataValues: [Object],
       _previousDataValues: [Object],
       _changed: {},
       _modelOptions: [Object],
       _options: [Object],
       isNewRecord: false
     }
   },
   queueIntegration: null,
   ticketTags: []
 } null undefined undefined false
 Error: not-acceptable
     at assertNodeErrorFree (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/WABinary/generic-utils.js:56:15)
     at query (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/socket.js:143:48)
     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
     at async assertSessions (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:201:28)
     at async /home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:370:21
     at async Object.transaction (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Utils/auth-utils.js:135:26)
     at async relayMessage (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:306:9)
     at async Object.sendMessage (/home/<USER>/whaconnect/backend/node_modules/@whiskeysockets/baileys/lib/Socket/messages-send.js:681:17)
     at async SendWhatsAppMessage (/home/<USER>/whaconnect/backend/dist/services/WbotServices/SendWhatsAppMessage.js:126:29)
     at async store (/home/<USER>/whaconnect/backend/dist/controllers/MessageController.js:137:17) {
   data: 406,
   isBoom: true,
   isServer: true,
   output: {
     statusCode: 500,
     payload: {
       statusCode: 500,
       error: 'Internal Server Error',
       message: 'An internal server error occurred'
     },
     headers: {}
   }
 }
 Error not-acceptable detectado <NAME_EMAIL>
 AppError {
   message: 'No se puede enviar el mensaje al grupo. Esto puede deberse a: 1) El bot fue removido del grupo, 2) El grupo fue eliminado, 3) Problemas de sincronización de WhatsApp. Por favor, verifica que el bot esté en el grupo y vuelve a intentar.',
   statusCode: 400
 }