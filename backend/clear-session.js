#!/usr/bin/env node

const Redis = require('ioredis');
const { Sequelize } = require('sequelize');

// Cargar variables de entorno
require('dotenv').config();

// Configuración de Redis
const redis = new Redis(process.env.REDIS_URI || 'redis://:whaconnect@127.0.0.1:6379');

// Configuración de base de datos
const sequelize = new Sequelize(process.env.DB_NAME || 'whaconnect', process.env.DB_USER || 'whaconnect', process.env.DB_PASS || 'whaconnect', {
  host: process.env.DB_HOST || 'localhost',
  dialect: 'postgres',
  logging: false
});

async function clearWhatsAppSession(whatsappId) {
  console.log(`🧹 Limpiando sesión completa para WhatsApp ID: ${whatsappId}`);
  
  try {
    // 1. Limpiar caché de Redis
    console.log('📦 Limpiando caché de Redis...');
    const sessionKeys = await redis.keys(`sessions:${whatsappId}:*`);
    if (sessionKeys.length > 0) {
      await redis.del(...sessionKeys);
      console.log(`✅ Eliminadas ${sessionKeys.length} claves de sesión de Redis`);
    } else {
      console.log('ℹ️  No se encontraron claves de sesión en Redis');
    }

    // 2. Limpiar tabla Baileys
    console.log('🗄️  Limpiando tabla Baileys...');
    const [results] = await sequelize.query(
      'DELETE FROM "Baileys" WHERE "whatsappId" = :whatsappId',
      {
        replacements: { whatsappId },
        type: sequelize.QueryTypes.DELETE
      }
    );
    console.log(`✅ Eliminados registros de Baileys: ${results}`);

    // 3. Resetear estado del WhatsApp
    console.log('🔄 Reseteando estado del WhatsApp...');
    await sequelize.query(
      'UPDATE "Whatsapps" SET "session" = \'\', "qrcode" = \'\', "status" = \'DISCONNECTED\', "retries" = 0 WHERE "id" = :whatsappId',
      {
        replacements: { whatsappId },
        type: sequelize.QueryTypes.UPDATE
      }
    );
    console.log('✅ Estado del WhatsApp reseteado');

    console.log(`🎉 Sesión del WhatsApp ID ${whatsappId} limpiada completamente`);
    console.log('💡 Ahora puedes reiniciar el backend y reconectar el WhatsApp');
    
  } catch (error) {
    console.error('❌ Error al limpiar la sesión:', error);
  } finally {
    await redis.quit();
    await sequelize.close();
  }
}

// Obtener WhatsApp ID de los argumentos de línea de comandos
const whatsappId = process.argv[2];

if (!whatsappId) {
  console.log('❌ Por favor proporciona el ID del WhatsApp');
  console.log('Uso: node clear-session.js <whatsapp_id>');
  console.log('Ejemplo: node clear-session.js 2');
  process.exit(1);
}

clearWhatsAppSession(whatsappId);
