import { WAMessage, delay } from "@whiskeysockets/baileys";
import * as Sentry from "@sentry/node";
import AppError from "../../errors/AppError";
import GetTicketWbot from "../../helpers/GetTicketWbot";
import Message from "../../models/Message";
import Ticket from "../../models/Ticket";
import Contact from "../../models/Contact";
import { isNil } from "lodash";

import formatBody from "../../helpers/Mustache";

interface Request {
  body: string;
  ticket: Ticket;
  quotedMsg?: Message;
  msdelay?: number;
  vCard?: Contact;
  isForwarded?: boolean;
}

const SendWhatsAppMessage = async ({
  body,
  ticket,
  quotedMsg,
  msdelay,
  vCard,
  isForwarded = false
}: Request): Promise<WAMessage> => {
  let options = {};
  const wbot = await GetTicketWbot(ticket);
  const contactNumber = await Contact.findByPk(ticket.contactId)

  let number: string;

  if (contactNumber.remoteJid && contactNumber.remoteJid !== "" && contactNumber.remoteJid.includes("@")) {
    number = contactNumber.remoteJid;
  } else {
    number = `${contactNumber.number}@${ticket.isGroup ? "g.us" : "s.whatsapp.net"
      }`;
  }

  if (quotedMsg) {
    const chatMessages = await Message.findOne({
      where: {
        id: quotedMsg.id
      }
    });

    if (chatMessages) {
      const msgFound = JSON.parse(chatMessages.dataJson);


      if (msgFound.message.extendedTextMessage !== undefined) {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              extendedTextMessage: msgFound.message.extendedTextMessage,
            }
          },
        };
      } else {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              conversation: msgFound.message.conversation,
            }
          },
        };
      }
    }
  }

  if (!isNil(vCard)) {
    const numberContact = vCard.number;
    const firstName = vCard.name.split(' ')[0];
    const lastName = String(vCard.name).replace(vCard.name.split(' ')[0], '')

    const vcard = `BEGIN:VCARD\n`
      + `VERSION:3.0\n`
      + `N:${lastName};${firstName};;;\n`
      + `FN:${vCard.name}\n`
      + `TEL;type=CELL;waid=${numberContact}:+${numberContact}\n`
      + `END:VCARD`;

    try {
      await delay(msdelay)
      const sentMessage = await wbot.sendMessage(
        number,
        {
          contacts: {
            displayName: `${vCard.name}`,
            contacts: [{ vcard }]
          }
        }
      );
      await ticket.update({ lastMessage: formatBody(vcard, ticket), imported: null });
      return sentMessage;
    } catch (err) {
      Sentry.captureException(err);
      console.log(err);
      throw new AppError("ERR_SENDING_WAPP_MSG");
    }
  };
  try {
    await delay(msdelay)

    // Validación deshabilitada temporalmente debido a problemas con Baileys
    if (ticket.isGroup) {
      console.log(`Enviando mensaje a grupo ${number} - Validaciones deshabilitadas temporalmente`);
      // Comentamos la validación problemática hasta que se resuelva el issue de Baileys
      /*
      try {
        const groupMetadata = await wbot.groupMetadata(number);
        const botNumber = wbot.user?.id?.split(':')[0];
        const isParticipant = groupMetadata.participants.some(p => p.id.split('@')[0] === botNumber);

        if (!isParticipant) {
          console.log(`Bot no está en el grupo ${number}`);
        }
      } catch (groupErr) {
        console.log(`Error verificando grupo ${number}:`, groupErr);
      }
      */
    }

    const sentMessage = await wbot.sendMessage(
      number,
      {
        text: formatBody(body, ticket),
        contextInfo: { forwardingScore: isForwarded ? 2 : 0, isForwarded: isForwarded ? true : false }
      },
      {
        ...options
      }
    );
    await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
    return sentMessage;
  } catch (err) {
    console.log(`erro ao enviar mensagem na company ${ticket.companyId} - `, body,
      ticket,
      quotedMsg,
      msdelay,
      vCard,
      isForwarded)
    Sentry.captureException(err);
    console.log(err);

    // Manejo específico para el error not-acceptable de Baileys
    if (err.message === "not-acceptable" || (err.data && err.data === 406)) {
      console.log(`Error not-acceptable detectado para ${ticket.isGroup ? 'grupo' : 'contacto'} ${number}`);

      if (ticket.isGroup) {
        throw new AppError("No se puede enviar el mensaje al grupo. Esto puede deberse a: 1) El bot fue removido del grupo, 2) El grupo fue eliminado, 3) Problemas de sincronización de WhatsApp. Por favor, verifica que el bot esté en el grupo y vuelve a intentar.");
      } else {
        throw new AppError("No se puede enviar el mensaje. El contacto puede haber bloqueado el bot o hay problemas de conectividad con WhatsApp.");
      }
    }

    // Manejo específico de errores de grupo
    if (err.message === "ERR_BOT_NOT_IN_GROUP") {
      throw new AppError("El bot no está en el grupo o fue removido");
    }
    if (err.message === "ERR_BOT_NO_PERMISSION_GROUP") {
      throw new AppError("El bot no tiene permisos para enviar mensajes en este grupo");
    }
    if (err.message === "ERR_GROUP_NOT_ACCESSIBLE") {
      throw new AppError("No se puede acceder al grupo. Puede que haya sido eliminado o el bot fue removido");
    }

    throw new AppError("ERR_SENDING_WAPP_MSG");
  }
};

export default SendWhatsAppMessage;
